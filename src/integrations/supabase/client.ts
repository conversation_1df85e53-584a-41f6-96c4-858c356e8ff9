
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { getAuthRedirectUrl } from '@/config/auth';

const SUPABASE_URL = "https://ptihuoxqjymxvvaotzaw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aWh1b3hxanlteHZ2YW90emF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIxNzE0MjEsImV4cCI6MjA1Nzc0NzQyMX0.ZJkuRTEq20KnGQjl91e5FGg5YiYukh08Nn57x41AYw8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: process.env.NODE_ENV === 'development'
  }
});

// Helper function to get the correct redirect URL for OAuth
export const getRedirectUrl = (path: string = '/dashboard') => {
  return getAuthRedirectUrl(path);
};

// Helper function to handle OAuth redirects
export const handleOAuthRedirect = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error("Error handling OAuth redirect:", error);
      return { success: false, error };
    }

    if (data.session) {
      console.log("OAuth redirect successful, user authenticated");
      return { success: true, session: data.session };
    }

    return { success: false, error: new Error("No session found after OAuth redirect") };
  } catch (error) {
    console.error("Error in OAuth redirect handler:", error);
    return { success: false, error };
  }
};
