
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import { UserCredits } from "./types";

export const useAddCreditsToUser = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, amount, silent = false }: { userId: string; amount: number; silent?: boolean }) => {
      try {
        // First check if the user has a credits record
        const { data: existingCredits, error: fetchError } = await supabase
          .from("user_credits")
          .select("*")
          .eq("user_id", userId)
          .order("updated_at", { ascending: false })
          .limit(1)
          .maybeSingle();
        
        if (fetchError) {
          console.error("Error fetching user credits:", fetchError);
          throw new Error(`Failed to fetch user credits: ${fetchError.message}`);
        }
        
        if (existingCredits) {
          // Update existing record
          const newBalance = existingCredits.credits_balance + amount;
          const { data, error } = await supabase
            .from("user_credits")
            .update({ credits_balance: newBalance, updated_at: new Date().toISOString() })
            .eq("id", existingCredits.id)
            .select()
            .single();
          
          if (error) {
            console.error("Error updating user credits:", error);
            if (error.message.includes("new row violates row-level security policy")) {
              throw new Error("Permission denied: Only administrators can add credits");
            }
            throw new Error(`Failed to update user credits: ${error.message}`);
          }
          
          return data;
        } else {
          try {
            const { data, error } = await supabase.rpc("create_user_credits", {
              user_id_param: userId,
              credits_balance_param: amount
            });
            
            if (error) {
              console.error("Error creating user credits:", error);
              if (error.message.includes("new row violates row-level security policy")) {
                throw new Error("Permission denied: Only administrators can add credits");
              }
              throw new Error(`Failed to create user credits: ${error.message}`);
            }
            
            // Fix: The RPC function returns an array, but we need a single object
            // Extract the first item from the array
            if (Array.isArray(data) && data.length > 0) {
              return data[0] as UserCredits;
            } else {
              throw new Error("No user credits record returned from create_user_credits");
            }
          } catch (rpcError: any) {
            console.error("Error in create_user_credits RPC:", rpcError);
            
            if (rpcError.message && rpcError.message.includes("row-level security policy")) {
              throw new Error("Permission denied: Only administrators can add credits");
            }
            
            // Don't try fallback insert if it's an RLS error
            if (rpcError.message && rpcError.message.includes("Permission denied")) {
              throw rpcError;
            }
            
            // Fallback to direct insert if RPC fails
            const { data: fallbackData, error: fallbackError } = await supabase
              .from("user_credits")
              .insert({
                user_id: userId,
                credits_balance: amount,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select()
              .single();
              
            if (fallbackError) {
              console.error("Fallback insert error:", fallbackError);
              if (fallbackError.message.includes("new row violates row-level security policy")) {
                throw new Error("Permission denied: Only administrators can add credits");
              }
              throw new Error(`Failed to add credits: ${fallbackError.message}`);
            }
            
            return fallbackData as UserCredits;
          }
        }
      } catch (error) {
        console.error("Error in addCreditsToUser:", error);
        throw error instanceof Error ? error : new Error("An unknown error occurred");
      }
    },
    onSuccess: (result, variables) => {
      // Only show toast notification if silent is false
      if (!variables.silent) {
        toast.success(`Added ${variables.amount} credits to user`);
      }
      
      // If this is the current user, update the cache
      if (user && result.user_id === user.id) {
        queryClient.setQueryData(["user-credits", user.id], result);
      }
    },
    onError: (error: Error) => {
      // Don't show toast for network errors during development demo mode
      if (error.message && error.message.includes("Failed to fetch")) {
        console.warn("Network error when adding credits - this is expected in demo mode");
        // We don't simulate success here anymore
        return;
      }
      
      // Format error message to avoid showing [object Object]
      const errorMessage = error instanceof Error 
        ? error.message 
        : (typeof error === 'object' && error !== null)
          ? JSON.stringify(error)
          : String(error);
          
      toast.error(`Failed to add credits: ${errorMessage}`);
    }
  });
};
