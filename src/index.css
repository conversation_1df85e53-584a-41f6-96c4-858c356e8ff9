@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 0 91% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 5.9% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.5rem;

    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 0 91% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 0 91% 50%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 91% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  .glass-card {
    @apply bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-lg dark:bg-black/60 dark:border-gray-800/50;
  }

  .glass-navbar {
    @apply bg-white/80 backdrop-blur-md border-b border-gray-200/20 shadow-sm dark:bg-black/60 dark:border-gray-800/20;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Custom animations */
  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .hover-up {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  .hover-link {
    @apply relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-youtube-red after:origin-bottom-right after:scale-x-0 hover:after:origin-bottom-left hover:after:scale-x-100 after:transition-transform after:duration-300;
  }
  
  /* Background gradients */
  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-br from-youtube-red/90 to-youtube-darkred dark:from-youtube-red/80 dark:to-youtube-darkred/90;
  }
  
  .bg-gradient-accent {
    @apply bg-gradient-to-tr from-gray-100 via-gray-50 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900;
  }

  /* Enhanced gradient backgrounds */
  .bg-gradient-hero {
    @apply bg-gradient-to-r from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900;
  }

  .bg-gradient-testimonial {
    @apply bg-gradient-to-br from-gray-50/70 via-white to-gray-100/70 dark:from-gray-900/70 dark:via-gray-800 dark:to-gray-900/70;
  }

  .bg-gradient-cta {
    @apply bg-gradient-to-tr from-gray-100 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900;
  }

  .bg-gradient-footer {
    @apply bg-gradient-to-b from-gray-100 to-white dark:from-gray-900 dark:to-gray-800;
  }

  /* Slight blur overlay on main content when navbar link is hovered */
  .blurred-bg #main-content::after {
    content: "";
    position: absolute;
    inset: 0;
    z-index: 50;
    pointer-events: none;
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    transition: backdrop-filter 0.4s cubic-bezier(.22,1,.36,1);
  }

  #main-content {
    position: relative;
  }
}
